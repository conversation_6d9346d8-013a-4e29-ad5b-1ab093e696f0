{"header": {"ID": "{{$guid}}", "application": "76A9FF99-64F9-4F72-9629-305CBE047902"}, "payload": {"userID": "XANDRH004400003", "startDate": "2025-01-01", "endDate": "2025-07-14", "providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "metricIDs": ["transactions_by_geo"], "filterValues": [{"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "customer_region_type", "value": "home_address"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "transactions_type", "value": "transactions_count"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "data_origin", "value": "own_data"}], "metricParameters": {"transactions_by_geo": {"geoType": "regionalUnit", "geo_location": "ΑΤΤΙΚΗ"}}, "merchantId": null}}